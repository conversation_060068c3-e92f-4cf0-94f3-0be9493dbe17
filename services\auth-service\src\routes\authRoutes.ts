import { Router } from 'express';
import rateLimit from 'express-rate-limit';
import {
  register,
  login,
  refreshToken,
  logout,
  logoutAll,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerification,
  healthCheck,
} from '../controllers/authController';
import {
  resolveTenant,
  authenticate,
  requireEmailVerification,
  checkTenantLimits,
} from '../middleware/auth';
import { validate } from '../utils/validation';
import {
  registerSchema,
  loginSchema,
  updateUserSchema,
  changePasswordSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  refreshTokenSchema,
  verifyEmailSchema,
  resendVerificationSchema,
} from '../utils/validation';

const router = Router();

// Rate limiting configurations
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  message: {
    success: false,
    message: 'Too many requests, please try again later',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 password reset attempts per hour
  message: {
    success: false,
    message: 'Too many password reset attempts, please try again later',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Health check (no tenant required)
router.get('/health', healthCheck);

// Apply tenant resolution to all routes except health check
router.use(resolveTenant);

// Public routes (no authentication required)
router.post(
  '/register',
  generalLimiter,
  checkTenantLimits('users'),
  validate(registerSchema),
  register
);

router.post(
  '/login',
  authLimiter,
  validate(loginSchema),
  login
);

router.post(
  '/refresh-token',
  generalLimiter,
  validate(refreshTokenSchema),
  refreshToken
);

router.post(
  '/forgot-password',
  passwordResetLimiter,
  validate(forgotPasswordSchema),
  forgotPassword
);

router.post(
  '/reset-password',
  passwordResetLimiter,
  validate(resetPasswordSchema),
  resetPassword
);

router.post(
  '/verify-email',
  generalLimiter,
  validate(verifyEmailSchema),
  verifyEmail
);

router.post(
  '/resend-verification',
  generalLimiter,
  validate(resendVerificationSchema),
  resendVerification
);

// Protected routes (authentication required)
router.use(authenticate);

router.post('/logout', generalLimiter, logout);

router.post('/logout-all', generalLimiter, logoutAll);

router.get('/profile', generalLimiter, getProfile);

router.put(
  '/profile',
  generalLimiter,
  validate(updateUserSchema),
  updateProfile
);

router.post(
  '/change-password',
  authLimiter,
  requireEmailVerification,
  validate(changePasswordSchema),
  changePassword
);

export default router;