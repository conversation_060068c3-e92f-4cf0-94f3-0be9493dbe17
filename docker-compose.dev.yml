version: '3.8'

services:
  # Databases
  postgres-master:
    image: postgres:15-alpine
    container_name: ecommerce-postgres-master
    environment:
      POSTGRES_DB: ecommerce_master
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_master_data:/var/lib/postgresql/data
      - ./shared/database/init:/docker-entrypoint-initdb.d
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d ecommerce_master"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: ecommerce-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Message Queue
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: ecommerce-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      <PERSON>BBITMQ_DEFAULT_PASS: password123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Node.js Services
  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile.dev
    container_name: ecommerce-api-gateway
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      PORT: 3000
      REDIS_URL: redis://redis:6379
      AUTH_SERVICE_URL: http://auth-service:3001
      FILE_SERVICE_URL: http://file-service:3002
      NOTIFICATION_SERVICE_URL: http://notification-service:3003
      TENANT_SERVICE_URL: http://tenant-service:8001
      PRODUCT_SERVICE_URL: http://product-service:8002
      ORDER_SERVICE_URL: http://order-service:8003
    volumes:
      - ./services/api-gateway:/app
      - /app/node_modules
    networks:
      - ecommerce-network
    depends_on:
      - redis
      - rabbitmq
    restart: unless-stopped

  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile.dev
    container_name: ecommerce-auth-service
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ***************************************************/ecommerce_master
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 24h
    volumes:
      - ./services/auth-service:/app
      - /app/node_modules
    networks:
      - ecommerce-network
    depends_on:
      - postgres-master
      - redis
    restart: unless-stopped

  file-service:
    build:
      context: ./services/file-service
      dockerfile: Dockerfile.dev
    container_name: ecommerce-file-service
    ports:
      - "3002:3002"
    environment:
      NODE_ENV: development
      PORT: 3002
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: 10485760
      ALLOWED_EXTENSIONS: jpg,jpeg,png,gif,pdf,doc,docx
    volumes:
      - ./services/file-service:/app
      - /app/node_modules
      - file_uploads:/app/uploads
    networks:
      - ecommerce-network
    restart: unless-stopped

  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: Dockerfile.dev
    container_name: ecommerce-notification-service
    ports:
      - "3003:3003"
    environment:
      NODE_ENV: development
      PORT: 3003
      REDIS_URL: redis://redis:6379
      RABBITMQ_URL: amqp://admin:password123@rabbitmq:5672
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USER: <EMAIL>
      SMTP_PASS: your-app-password
    volumes:
      - ./services/notification-service:/app
      - /app/node_modules
    networks:
      - ecommerce-network
    depends_on:
      - redis
      - rabbitmq
    restart: unless-stopped

  # FastAPI Services
  tenant-service:
    build:
      context: ./services/tenant-service
      dockerfile: Dockerfile.dev
    container_name: ecommerce-tenant-service
    ports:
      - "8001:8001"
    environment:
      ENVIRONMENT: development
      DATABASE_URL: ***************************************************/ecommerce_master
      REDIS_URL: redis://redis:6379
    volumes:
      - ./services/tenant-service:/app
    networks:
      - ecommerce-network
    depends_on:
      - postgres-master
      - redis
    restart: unless-stopped

  product-service:
    build:
      context: ./services/product-service
      dockerfile: Dockerfile.dev
    container_name: ecommerce-product-service
    ports:
      - "8002:8002"
    environment:
      ENVIRONMENT: development
      REDIS_URL: redis://redis:6379
    volumes:
      - ./services/product-service:/app
    networks:
      - ecommerce-network
    depends_on:
      - redis
    restart: unless-stopped

  order-service:
    build:
      context: ./services/order-service
      dockerfile: Dockerfile.dev
    container_name: ecommerce-order-service
    ports:
      - "8003:8003"
    environment:
      ENVIRONMENT: development
      REDIS_URL: redis://redis:6379
      RABBITMQ_URL: amqp://admin:password123@rabbitmq:5672
    volumes:
      - ./services/order-service:/app
    networks:
      - ecommerce-network
    depends_on:
      - redis
      - rabbitmq
    restart: unless-stopped

  analytics-service:
    build:
      context: ./services/analytics-service
      dockerfile: Dockerfile.dev
    container_name: ecommerce-analytics-service
    ports:
      - "8004:8004"
    environment:
      ENVIRONMENT: development
      REDIS_URL: redis://redis:6379
    volumes:
      - ./services/analytics-service:/app
    networks:
      - ecommerce-network
    depends_on:
      - redis
    restart: unless-stopped

  recommendation-service:
    build:
      context: ./services/recommendation-service
      dockerfile: Dockerfile.dev
    container_name: ecommerce-recommendation-service
    ports:
      - "8005:8005"
    environment:
      ENVIRONMENT: development
      REDIS_URL: redis://redis:6379
    volumes:
      - ./services/recommendation-service:/app
    networks:
      - ecommerce-network
    depends_on:
      - redis
    restart: unless-stopped

networks:
  ecommerce-network:
    driver: bridge

volumes:
  postgres_master_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  file_uploads:
    driver: local