from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    # Application settings
    APP_NAME: str = "Tenant Service"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True

    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 8001

    # Security settings
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Database settings
    DATABASE_URL: str = "postgresql://admin:password123@localhost:5432/ecommerce_master"
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    DATABASE_POOL_TIMEOUT: int = 30
    DATABASE_POOL_RECYCLE: int = 3600

    # Redis settings
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_DB: int = 0
    REDIS_POOL_SIZE: int = 10

    # CORS settings
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8000",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",
        "http://127.0.0.1:8000"
    ]

    ALLOWED_HOSTS: List[str] = ["*"]

    # Tenant settings
    DEFAULT_TENANT_PLAN: str = "basic"
    MAX_TENANTS_PER_PLAN: dict = {
        "basic": 1000,
        "premium": 5000,
        "enterprise": -1  # unlimited
    }

    TENANT_LIMITS: dict = {
        "basic": {
            "max_users": 100,
            "max_products": 1000,
            "max_storage_mb": 1024
        },
        "premium": {
            "max_users": 1000,
            "max_products": 10000,
            "max_storage_mb": 10240
        },
        "enterprise": {
            "max_users": -1,  # unlimited
            "max_products": -1,  # unlimited
            "max_storage_mb": -1  # unlimited
        }
    }

    # Email settings (for notifications)
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_TLS: bool = True

    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Rate limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 900  # 15 minutes

    # Pagination
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100

    # File upload settings
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = [
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/webp"
    ]

    # External services
    AUTH_SERVICE_URL: str = "http://auth-service:3001"
    PRODUCT_SERVICE_URL: str = "http://product-service:8002"
    ORDER_SERVICE_URL: str = "http://order-service:8003"

    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

        @classmethod
        def parse_env_var(cls, field_name: str, raw_val: str) -> any:
            if field_name in ["ALLOWED_ORIGINS", "ALLOWED_HOSTS", "ALLOWED_FILE_TYPES"]:
                return [x.strip() for x in raw_val.split(",")]
            return cls.json_loads(raw_val)


# Create settings instance
settings = Settings()

# Validate settings
def validate_settings():
    """Validate critical settings"""
    errors = []

    if not settings.SECRET_KEY or settings.SECRET_KEY == "your-super-secret-key-change-in-production":
        if settings.ENVIRONMENT == "production":
            errors.append("SECRET_KEY must be set in production")

    if not settings.DATABASE_URL:
        errors.append("DATABASE_URL is required")

    if settings.ENVIRONMENT == "production" and settings.DEBUG:
        errors.append("DEBUG should be False in production")

    if errors:
        raise ValueError(f"Configuration errors: {', '.join(errors)}")

# Validate on import
validate_settings()