# Multi-Tenant E-commerce System

Hệ thống thương mại điện tử multi-tenants sử dụng kiến trúc microservices hybrid với Node.js và FastAPI.

## 🏗️ Kiến trúc

### Node.js Services
- **API Gateway** (Port 3000) - Request routing, authentication, rate limiting
- **Auth Service** (Port 3001) - JWT authentication, user management
- **File Service** (Port 3002) - File upload, image processing
- **Notification Service** (Port 3003) - Email, SMS, real-time notifications

### FastAPI Services
- **Tenant Service** (Port 8001) - Tenant management, database provisioning
- **Product Service** (Port 8002) - Product catalog, categories, inventory
- **Order Service** (Port 8003) - Shopping cart, order processing, payments
- **Analytics Service** (Port 8004) - Sales analytics, reporting
- **Recommendation Service** (Port 8005) - ML-based product recommendations

### Infrastructure
- **PostgreSQL** - Master database + tenant databases
- **Redis** - Caching, sessions, rate limiting
- **RabbitMQ** - Message queue for async processing

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.11+
- Docker & Docker Compose
- PostgreSQL 15+

### Development Setup

1. **Clone repository**
```bash
git clone <repository-url>
cd multi-tenant-ecommerce
```

2. **Install dependencies**
```bash
npm install
npm run setup:services
```

3. **Start infrastructure services**
```bash
docker-compose -f docker-compose.dev.yml up postgres-master redis rabbitmq -d
```

4. **Setup environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Run database migrations**
```bash
npm run db:migrate
```

6. **Start all services**
```bash
npm run dev
```

### Using Docker (Recommended)

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up

# Start specific services
docker-compose -f docker-compose.dev.yml up postgres-master redis rabbitmq

# View logs
docker-compose -f docker-compose.dev.yml logs -f api-gateway
```

## 📁 Project Structure

```
multi-tenant-ecommerce/
├── services/
│   ├── api-gateway/          # Node.js API Gateway
│   ├── auth-service/         # Node.js Auth Service
│   ├── file-service/         # Node.js File Service
│   ├── notification-service/ # Node.js Notification Service
│   ├── tenant-service/       # FastAPI Tenant Service
│   ├── product-service/      # FastAPI Product Service
│   ├── order-service/        # FastAPI Order Service
│   ├── analytics-service/    # FastAPI Analytics Service
│   └── recommendation-service/ # FastAPI Recommendation Service
├── shared/
│   ├── database/            # Database schemas, migrations
│   ├── types/              # Shared TypeScript types
│   ├── utils/              # Shared utilities
│   └── config/             # Shared configuration
├── infrastructure/         # Docker, K8s, Terraform configs
├── docs/                  # Documentation
├── tests/                 # Integration & E2E tests
└── docker-compose.dev.yml # Development environment
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev                 # Start all Node.js services
npm run dev:gateway        # Start API Gateway only
npm run dev:auth           # Start Auth Service only

# Building
npm run build              # Build all services
npm run build:gateway      # Build API Gateway only

# Testing
npm test                   # Run all tests
npm run test:watch         # Run tests in watch mode
npm run test:coverage      # Run tests with coverage

# Code Quality
npm run lint               # Lint code
npm run lint:fix           # Fix linting issues
npm run format             # Format code with Prettier

# Docker
npm run docker:dev         # Start development environment
npm run docker:prod        # Start production environment
```

### Environment Variables

Create `.env` file in root directory:

```env
# Database
DATABASE_URL=postgresql://admin:password123@localhost:5432/ecommerce_master
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Services URLs
AUTH_SERVICE_URL=http://localhost:3001
FILE_SERVICE_URL=http://localhost:3002
NOTIFICATION_SERVICE_URL=http://localhost:3003
TENANT_SERVICE_URL=http://localhost:8001
PRODUCT_SERVICE_URL=http://localhost:8002
ORDER_SERVICE_URL=http://localhost:8003

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf
```