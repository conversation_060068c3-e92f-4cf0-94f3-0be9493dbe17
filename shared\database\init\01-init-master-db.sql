-- Master Database Initialization Script
-- This script creates the master database schema for multi-tenant e-commerce system

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON> enum types
CREATE TYPE tenant_status AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE tenant_plan AS ENUM ('basic', 'premium', 'enterprise');
CREATE TYPE admin_role AS ENUM ('super_admin', 'support');
CREATE TYPE service_type AS ENUM ('nodejs', 'fastapi');

-- Tenants table
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    custom_domain VARCHAR(255),
    database_name VARCHAR(100) UNIQUE NOT NULL,
    status tenant_status DEFAULT 'active',
    plan tenant_plan DEFAULT 'basic',
    settings JSONB DEFAULT '{}',
    max_users INTEGER DEFAULT 100,
    max_products INTEGER DEFAULT 1000,
    max_storage_mb INTEGER DEFAULT 1024,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- System Admins table
CREATE TABLE system_admins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role admin_role DEFAULT 'support',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Service Registry table
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    type service_type NOT NULL,
    url VARCHAR(255) NOT NULL,
    health_check_url VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    version VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tenant Database Templates table
CREATE TABLE tenant_db_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    sql_script TEXT NOT NULL,
    version VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tenant Migrations table
CREATE TABLE tenant_migrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    migration_name VARCHAR(255) NOT NULL,
    version VARCHAR(20) NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, migration_name)
);

-- Audit Log table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE SET NULL,
    admin_id UUID REFERENCES system_admins(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_tenants_subdomain ON tenants(subdomain);
CREATE INDEX idx_tenants_custom_domain ON tenants(custom_domain);
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_system_admins_email ON system_admins(email);
CREATE INDEX idx_services_name ON services(name);
CREATE INDEX idx_services_type ON services(type);
CREATE INDEX idx_tenant_migrations_tenant_id ON tenant_migrations(tenant_id);
CREATE INDEX idx_audit_logs_tenant_id ON audit_logs(tenant_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_admins_updated_at BEFORE UPDATE ON system_admins
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system admin (password: admin123)
INSERT INTO system_admins (email, password_hash, role) VALUES
('<EMAIL>', crypt('admin123', gen_salt('bf')), 'super_admin');

-- Insert default services
INSERT INTO services (name, type, url, health_check_url, version) VALUES
('api-gateway', 'nodejs', 'http://api-gateway:3000', 'http://api-gateway:3000/health', '1.0.0'),
('auth-service', 'nodejs', 'http://auth-service:3001', 'http://auth-service:3001/health', '1.0.0'),
('file-service', 'nodejs', 'http://file-service:3002', 'http://file-service:3002/health', '1.0.0'),
('notification-service', 'nodejs', 'http://notification-service:3003', 'http://notification-service:3003/health', '1.0.0'),
('tenant-service', 'fastapi', 'http://tenant-service:8001', 'http://tenant-service:8001/health', '1.0.0'),
('product-service', 'fastapi', 'http://product-service:8002', 'http://product-service:8002/health', '1.0.0'),
('order-service', 'fastapi', 'http://order-service:8003', 'http://order-service:8003/health', '1.0.0'),
('analytics-service', 'fastapi', 'http://analytics-service:8004', 'http://analytics-service:8004/health', '1.0.0'),
('recommendation-service', 'fastapi', 'http://recommendation-service:8005', 'http://recommendation-service:8005/health', '1.0.0');