# Setup project structure
Write-Host "Creating project structure..." -ForegroundColor Green

# Services directories
$services = @(
    "services/api-gateway",
    "services/auth-service",
    "services/file-service",
    "services/notification-service",
    "services/tenant-service",
    "services/product-service",
    "services/order-service",
    "services/analytics-service",
    "services/recommendation-service"
)

# Shared directories
$shared = @(
    "shared/database/migrations",
    "shared/database/seeds",
    "shared/types",
    "shared/utils",
    "shared/config"
)

# Infrastructure directories
$infrastructure = @(
    "infrastructure/docker",
    "infrastructure/kubernetes",
    "infrastructure/terraform",
    "infrastructure/monitoring"
)

# Documentation directories
$docs = @(
    "docs/api",
    "docs/deployment",
    "docs/development"
)

# Test directories
$tests = @(
    "tests/integration",
    "tests/e2e",
    "tests/load"
)

# Create all directories
$allDirs = $services + $shared + $infrastructure + $docs + $tests

foreach ($dir in $allDirs) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
    Write-Host "Created: $dir" -ForegroundColor Yellow
}

Write-Host "Project structure created successfully!" -ForegroundColor Green