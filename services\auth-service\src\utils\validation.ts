import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { ValidationError } from '../types';

// Custom password validation
const passwordSchema = Joi.string()
  .min(8)
  .max(128)
  .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
  .message(
    'Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character'
  )
  .required();

// Email validation
const emailSchema = Joi.string()
  .email({ tlds: { allow: false } })
  .max(255)
  .required();

// User registration schema
export const registerSchema = Joi.object({
  email: emailSchema,
  password: passwordSchema,
  first_name: Joi.string().min(1).max(100).optional(),
  last_name: Joi.string().min(1).max(100).optional(),
  phone: Joi.string()
    .pattern(/^\+?[1-9]\d{1,14}$/)
    .message('Phone number must be in valid international format')
    .optional(),
  role: Joi.string().valid('admin', 'staff', 'customer').default('customer'),
});

// User login schema
export const loginSchema = Joi.object({
  email: emailSchema,
  password: Joi.string().required(),
});

// Update user schema
export const updateUserSchema = Joi.object({
  first_name: Joi.string().min(1).max(100).optional(),
  last_name: Joi.string().min(1).max(100).optional(),
  phone: Joi.string()
    .pattern(/^\+?[1-9]\d{1,14}$/)
    .message('Phone number must be in valid international format')
    .optional(),
  status: Joi.string().valid('active', 'inactive', 'suspended').optional(),
});

// Change password schema
export const changePasswordSchema = Joi.object({
  current_password: Joi.string().required(),
  new_password: passwordSchema,
});

// Forgot password schema
export const forgotPasswordSchema = Joi.object({
  email: emailSchema,
});

// Reset password schema
export const resetPasswordSchema = Joi.object({
  token: Joi.string().length(64).hex().required(),
  new_password: passwordSchema,
});

// Refresh token schema
export const refreshTokenSchema = Joi.object({
  refresh_token: Joi.string().uuid().required(),
});

// Verify email schema
export const verifyEmailSchema = Joi.object({
  token: Joi.string().length(64).hex().required(),
});

// Resend verification schema
export const resendVerificationSchema = Joi.object({
  email: emailSchema,
});

// Pagination schema
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sort: Joi.string().valid('created_at', 'updated_at', 'email', 'name').default('created_at'),
  order: Joi.string().valid('asc', 'desc').default('desc'),
});

// Search schema
export const searchSchema = Joi.object({
  q: Joi.string().min(1).max(255).optional(),
  role: Joi.string().valid('admin', 'staff', 'customer').optional(),
  status: Joi.string().valid('active', 'inactive', 'suspended').optional(),
  email_verified: Joi.boolean().optional(),
}).concat(paginationSchema);

/**
 * Validation middleware factory
 */
export function validate(schema: Joi.ObjectSchema, property: 'body' | 'query' | 'params' = 'body') {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true,
      convert: true,
    });

    if (error) {
      const errors = error.details.map(detail => detail.message);
      const validationError = new ValidationError(
        `Validation failed: ${errors.join(', ')}`
      );
      return next(validationError);
    }

    // Replace the request property with validated and sanitized data
    req[property] = value;
    next();
  };
}

/**
 * Validate UUID parameter
 */
export const validateUUID = (paramName: string) => {
  const schema = Joi.object({
    [paramName]: Joi.string().uuid().required(),
  });

  return validate(schema, 'params');
};

/**
 * Sanitize string input
 */
export function sanitizeString(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .substring(0, 1000); // Limit length
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const { error } = emailSchema.validate(email);
  return !error;
}

/**
 * Validate phone number format
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
}

/**
 * Validate subdomain format
 */
export function isValidSubdomain(subdomain: string): boolean {
  const subdomainRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/;
  return (
    subdomain.length >= 3 &&
    subdomain.length <= 63 &&
    subdomainRegex.test(subdomain) &&
    !subdomain.startsWith('-') &&
    !subdomain.endsWith('-')
  );
}

/**
 * Validate domain format
 */
export function isValidDomain(domain: string): boolean {
  const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/;
  return (
    domain.length >= 4 &&
    domain.length <= 253 &&
    domainRegex.test(domain) &&
    domain.includes('.')
  );
}

/**
 * Extract and validate tenant identifier from request
 */
export function extractTenantIdentifier(req: Request): string | null {
  // Try to get from subdomain
  const host = req.get('host');
  if (host) {
    const parts = host.split('.');
    if (parts.length >= 3) {
      const subdomain = parts[0];
      if (isValidSubdomain(subdomain)) {
        return subdomain;
      }
    }

    // Try custom domain
    if (isValidDomain(host)) {
      return host;
    }
  }

  // Try to get from header
  const tenantHeader = req.get('X-Tenant-ID');
  if (tenantHeader && isValidSubdomain(tenantHeader)) {
    return tenantHeader;
  }

  return null;
}