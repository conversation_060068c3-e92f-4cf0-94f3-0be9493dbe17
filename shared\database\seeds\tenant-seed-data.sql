-- Tenant Database Seed Data
-- This script inserts sample data for a new tenant

-- Insert default admin user (password: admin123)
INSERT INTO users (email, password_hash, first_name, last_name, role, email_verified) VALUES
('<EMAIL>', crypt('admin123', gen_salt('bf')), 'Admin', 'User', 'admin', true);

-- Insert sample categories
INSERT INTO categories (name, slug, description, is_active, sort_order) VALUES
('Electronics', 'electronics', 'Electronic devices and gadgets', true, 1),
('Clothing', 'clothing', 'Fashion and apparel', true, 2),
('Books', 'books', 'Books and literature', true, 3),
('Home & Garden', 'home-garden', 'Home improvement and gardening', true, 4),
('Sports', 'sports', 'Sports and outdoor activities', true, 5);

-- Insert subcategories
INSERT INTO categories (name, slug, description, parent_id, is_active, sort_order) VALUES
('Smartphones', 'smartphones', 'Mobile phones and accessories',
    (SELECT id FROM categories WHERE slug = 'electronics'), true, 1),
('Laptops', 'laptops', 'Laptop computers',
    (SELECT id FROM categories WHERE slug = 'electronics'), true, 2),
('Men''s Clothing', 'mens-clothing', 'Clothing for men',
    (SELECT id FROM categories WHERE slug = 'clothing'), true, 1),
('Women''s Clothing', 'womens-clothing', 'Clothing for women',
    (SELECT id FROM categories WHERE slug = 'clothing'), true, 2),
('Fiction', 'fiction', 'Fiction books',
    (SELECT id FROM categories WHERE slug = 'books'), true, 1);

-- Insert sample products
INSERT INTO products (name, slug, description, short_description, sku, price, compare_price, cost_price,
    inventory_quantity, status, featured, seo_title, seo_description) VALUES
('iPhone 15 Pro', 'iphone-15-pro',
    'The latest iPhone with advanced camera system and A17 Pro chip. Features titanium design and Action Button.',
    'Latest iPhone with titanium design and A17 Pro chip',
    'IPHONE15PRO-128', 999.00, 1099.00, 750.00, 50, 'active', true,
    'iPhone 15 Pro - Latest Apple Smartphone',
    'Buy the new iPhone 15 Pro with titanium design, A17 Pro chip, and advanced camera system.'),

('MacBook Air M2', 'macbook-air-m2',
    'Supercharged by M2 chip. The redesigned MacBook Air is more portable than ever and weighs just 2.7 pounds.',
    'Lightweight laptop with M2 chip and all-day battery',
    'MBA-M2-256', 1199.00, 1299.00, 900.00, 25, 'active', true,
    'MacBook Air M2 - Lightweight Laptop',
    'MacBook Air with M2 chip offers incredible performance in an ultra-portable design.'),

('Men''s Cotton T-Shirt', 'mens-cotton-tshirt',
    'Comfortable 100% cotton t-shirt perfect for everyday wear. Available in multiple colors and sizes.',
    'Comfortable 100% cotton t-shirt',
    'TSHIRT-MEN-001', 29.99, 39.99, 15.00, 100, 'active', false,
    'Men''s Cotton T-Shirt - Comfortable Everyday Wear',
    'High-quality cotton t-shirt for men. Comfortable, durable, and available in various colors.'),

('Women''s Summer Dress', 'womens-summer-dress',
    'Elegant summer dress made from breathable fabric. Perfect for casual outings and special occasions.',
    'Elegant summer dress in breathable fabric',
    'DRESS-WOM-001', 79.99, 99.99, 40.00, 30, 'active', true,
    'Women''s Summer Dress - Elegant and Comfortable',
    'Beautiful summer dress for women. Elegant design with comfortable, breathable fabric.'),

('The Great Gatsby', 'the-great-gatsby',
    'Classic American novel by F. Scott Fitzgerald. A timeless story of love, wealth, and the American Dream.',
    'Classic American novel by F. Scott Fitzgerald',
    'BOOK-GATSBY-001', 14.99, 19.99, 8.00, 75, 'active', false,
    'The Great Gatsby - Classic American Novel',
    'Read the timeless classic The Great Gatsby by F. Scott Fitzgerald. A must-read American novel.');

-- Link products to categories
INSERT INTO product_categories (product_id, category_id) VALUES
((SELECT id FROM products WHERE sku = 'IPHONE15PRO-128'),
 (SELECT id FROM categories WHERE slug = 'smartphones')),
((SELECT id FROM products WHERE sku = 'MBA-M2-256'),
 (SELECT id FROM categories WHERE slug = 'laptops')),
((SELECT id FROM products WHERE sku = 'TSHIRT-MEN-001'),
 (SELECT id FROM categories WHERE slug = 'mens-clothing')),
((SELECT id FROM products WHERE sku = 'DRESS-WOM-001'),
 (SELECT id FROM categories WHERE slug = 'womens-clothing')),
((SELECT id FROM products WHERE sku = 'BOOK-GATSBY-001'),
 (SELECT id FROM categories WHERE slug = 'fiction'));

-- Insert product variants for t-shirt (different sizes and colors)
INSERT INTO product_variants (product_id, sku, name, price, inventory_quantity, attributes) VALUES
((SELECT id FROM products WHERE sku = 'TSHIRT-MEN-001'), 'TSHIRT-MEN-001-S-BLK', 'Small Black', 29.99, 20, '{"size": "S", "color": "Black"}'),
((SELECT id FROM products WHERE sku = 'TSHIRT-MEN-001'), 'TSHIRT-MEN-001-M-BLK', 'Medium Black', 29.99, 25, '{"size": "M", "color": "Black"}'),
((SELECT id FROM products WHERE sku = 'TSHIRT-MEN-001'), 'TSHIRT-MEN-001-L-BLK', 'Large Black', 29.99, 20, '{"size": "L", "color": "Black"}'),
((SELECT id FROM products WHERE sku = 'TSHIRT-MEN-001'), 'TSHIRT-MEN-001-S-WHT', 'Small White', 29.99, 15, '{"size": "S", "color": "White"}'),
((SELECT id FROM products WHERE sku = 'TSHIRT-MEN-001'), 'TSHIRT-MEN-001-M-WHT', 'Medium White', 29.99, 20, '{"size": "M", "color": "White"}'),
((SELECT id FROM products WHERE sku = 'TSHIRT-MEN-001'), 'TSHIRT-MEN-001-L-WHT', 'Large White', 29.99, 15, '{"size": "L", "color": "White"}');

-- Insert sample coupons
INSERT INTO coupons (code, name, description, type, value, minimum_amount, usage_limit, is_active, expires_at) VALUES
('WELCOME10', 'Welcome Discount', 'Get 10% off your first order', 'percentage', 10.00, 50.00, 100, true,
    CURRENT_TIMESTAMP + INTERVAL '30 days'),
('SAVE20', 'Save $20', 'Get $20 off orders over $100', 'fixed_amount', 20.00, 100.00, 50, true,
    CURRENT_TIMESTAMP + INTERVAL '15 days'),
('SUMMER15', 'Summer Sale', '15% off summer collection', 'percentage', 15.00, 75.00, NULL, true,
    CURRENT_TIMESTAMP + INTERVAL '60 days');

-- Insert default settings
INSERT INTO settings (key, value, type, description, is_public) VALUES
('store_name', 'My Store', 'string', 'Store name displayed to customers', true),
('store_description', 'Your one-stop shop for quality products', 'string', 'Store description', true),
('currency', 'USD', 'string', 'Default currency', true),
('tax_rate', '8.5', 'number', 'Default tax rate percentage', false),
('shipping_rate', '9.99', 'number', 'Default shipping rate', true),
('free_shipping_threshold', '75.00', 'number', 'Minimum order for free shipping', true),
('allow_guest_checkout', 'true', 'boolean', 'Allow customers to checkout without account', true),
('require_email_verification', 'true', 'boolean', 'Require email verification for new accounts', false),
('max_cart_items', '50', 'number', 'Maximum items allowed in cart', false),
('low_stock_threshold', '5', 'number', 'Alert when inventory is below this number', false),
('store_email', '<EMAIL>', 'string', 'Store contact email', true),
('store_phone', '******-0123', 'string', 'Store contact phone', true),
('store_address', '{"street": "123 Main St", "city": "Anytown", "state": "CA", "zip": "12345", "country": "US"}', 'json', 'Store physical address', true),
('social_media', '{"facebook": "", "twitter": "", "instagram": ""}', 'json', 'Social media links', true),
('seo_title', 'My Store - Quality Products Online', 'string', 'Default SEO title', true),
('seo_description', 'Shop quality products at My Store. Fast shipping, great prices, excellent customer service.', 'string', 'Default SEO description', true);

-- Insert sample customer (password: customer123)
INSERT INTO users (email, password_hash, first_name, last_name, role, email_verified) VALUES
('<EMAIL>', crypt('customer123', gen_salt('bf')), 'John', 'Doe', 'customer', true);

-- Insert sample address for customer
INSERT INTO addresses (user_id, type, first_name, last_name, address_line_1, city, state, postal_code, country, phone, is_default) VALUES
((SELECT id FROM users WHERE email = '<EMAIL>'), 'shipping', 'John', 'Doe', '456 Oak Avenue', 'Somewhere', 'NY', '54321', 'US', '******-0456', true),
((SELECT id FROM users WHERE email = '<EMAIL>'), 'billing', 'John', 'Doe', '456 Oak Avenue', 'Somewhere', 'NY', '54321', 'US', '******-0456', true);