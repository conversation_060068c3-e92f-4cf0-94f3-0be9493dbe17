{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@config/*": ["./config/*"], "@controllers/*": ["./controllers/*"], "@middleware/*": ["./middleware/*"], "@models/*": ["./models/*"], "@routes/*": ["./routes/*"], "@services/*": ["./services/*"], "@types/*": ["./types/*"], "@utils/*": ["./utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}