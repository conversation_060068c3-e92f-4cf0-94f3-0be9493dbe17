import { Pool, PoolClient } from 'pg';
import { createClient } from 'redis';

// Master database configuration
const masterDbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  user: process.env.DB_USER || 'admin',
  password: process.env.DB_PASSWORD || 'password123',
  database: process.env.DB_NAME || 'ecommerce_master',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

// Redis configuration
const redisConfig = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
};

// Master database pool
export const masterPool = new Pool(masterDbConfig);

// Redis client
export const redisClient = createClient(redisConfig);

// Tenant database pools cache
const tenantPools = new Map<string, Pool>();

/**
 * Get or create a database pool for a specific tenant
 */
export async function getTenantPool(tenantId: string): Promise<Pool> {
  if (tenantPools.has(tenantId)) {
    return tenantPools.get(tenantId)!;
  }

  // Get tenant database name from master database
  const client = await masterPool.connect();
  try {
    const result = await client.query(
      'SELECT database_name FROM tenants WHERE id = $1 AND status = $2',
      [tenantId, 'active']
    );

    if (result.rows.length === 0) {
      throw new Error(`Active tenant with ID ${tenantId} not found`);
    }

    const { database_name } = result.rows[0];

    // Create new pool for tenant database
    const tenantPool = new Pool({
      ...masterDbConfig,
      database: database_name,
    });

    tenantPools.set(tenantId, tenantPool);
    return tenantPool;

  } finally {
    client.release();
  }
}

/**
 * Get tenant information by subdomain
 */
export async function getTenantBySubdomain(subdomain: string): Promise<any> {
  const client = await masterPool.connect();
  try {
    const result = await client.query(
      'SELECT id, name, subdomain, database_name, status, plan, settings FROM tenants WHERE subdomain = $1',
      [subdomain]
    );

    return result.rows[0] || null;
  } finally {
    client.release();
  }
}

/**
 * Get tenant information by custom domain
 */
export async function getTenantByDomain(domain: string): Promise<any> {
  const client = await masterPool.connect();
  try {
    const result = await client.query(
      'SELECT id, name, subdomain, database_name, status, plan, settings FROM tenants WHERE custom_domain = $1',
      [domain]
    );

    return result.rows[0] || null;
  } finally {
    client.release();
  }
}

/**
 * Initialize database connections
 */
export async function initializeDatabase(): Promise<void> {
  try {
    // Test master database connection
    await masterPool.query('SELECT 1');
    console.log('✅ Master database connected successfully');

    // Initialize Redis connection
    await redisClient.connect();
    console.log('✅ Redis connected successfully');

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

/**
 * Close all database connections
 */
export async function closeDatabase(): Promise<void> {
  try {
    // Close all tenant pools
    for (const [tenantId, pool] of tenantPools) {
      await pool.end();
      console.log(`✅ Closed tenant pool: ${tenantId}`);
    }
    tenantPools.clear();

    // Close master pool
    await masterPool.end();
    console.log('✅ Master database connection closed');

    // Close Redis connection
    await redisClient.quit();
    console.log('✅ Redis connection closed');

  } catch (error) {
    console.error('❌ Error closing database connections:', error);
  }
}

/**
 * Execute query on tenant database
 */
export async function queryTenant(
  tenantId: string,
  text: string,
  params?: any[]
): Promise<any> {
  const pool = await getTenantPool(tenantId);
  const client = await pool.connect();

  try {
    return await client.query(text, params);
  } finally {
    client.release();
  }
}

/**
 * Execute transaction on tenant database
 */
export async function transactionTenant<T>(
  tenantId: string,
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const pool = await getTenantPool(tenantId);
  const client = await pool.connect();

  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}