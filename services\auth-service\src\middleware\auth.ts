import { Request, Response, NextFunction } from 'express';
import {
  AuthenticatedRequest,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
} from '../types';
import {
  verifyAccessToken,
  extractTokenFromHeader,
  isTokenBlacklisted,
} from '../utils/jwt';
import { extractTenantIdentifier } from '../utils/validation';
import {
  getTenantBySubdomain,
  getTenantByDomain,
  queryTenant,
} from '../config/database';

/**
 * Middleware to resolve tenant from request
 */
export async function resolveTenant(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const tenantIdentifier = extractTenantIdentifier(req);

    if (!tenantIdentifier) {
      throw new NotFoundError('Tenant not found');
    }

    // Try to get tenant by subdomain first
    let tenant = await getTenantBySubdomain(tenantIdentifier);

    // If not found, try by custom domain
    if (!tenant) {
      tenant = await getTenantByDomain(tenantIdentifier);
    }

    if (!tenant) {
      throw new NotFoundError('Tenant not found');
    }

    if (tenant.status !== 'active') {
      throw new ForbiddenError('Tenant is not active');
    }

    // Attach tenant to request
    req.tenant = tenant;
    req.tenant_id = tenant.id;

    next();
  } catch (error) {
    next(error);
  }
}

/**
 * Middleware to authenticate user with JWT
 */
export async function authenticate(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.get('Authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      throw new UnauthorizedError('Access token required');
    }

    // Check if token is blacklisted
    if (await isTokenBlacklisted(token)) {
      throw new UnauthorizedError('Token has been revoked');
    }

    // Verify token
    const payload = verifyAccessToken(token);

    // Ensure tenant context matches
    if (req.tenant_id && payload.tenant_id !== req.tenant_id) {
      throw new ForbiddenError('Token does not match tenant context');
    }

    // Get user from tenant database
    const userResult = await queryTenant(
      payload.tenant_id,
      'SELECT id, email, first_name, last_name, phone, role, status, email_verified, last_login, created_at, updated_at FROM users WHERE id = $1',
      [payload.user_id]
    );

    if (userResult.rows.length === 0) {
      throw new UnauthorizedError('User not found');
    }

    const user = userResult.rows[0];

    if (user.status !== 'active') {
      throw new ForbiddenError('User account is not active');
    }

    // Attach user to request
    req.user = user;

    // Update last login time (async, don't wait)
    queryTenant(
      payload.tenant_id,
      'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
      [user.id]
    ).catch(console.error);

    next();
  } catch (error) {
    next(error);
  }
}

/**
 * Middleware to require specific roles
 */
export function requireRole(...roles: string[]) {
  return (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): void => {
    if (!req.user) {
      return next(new UnauthorizedError('Authentication required'));
    }

    if (!roles.includes(req.user.role)) {
      return next(
        new ForbiddenError(
          `Access denied. Required roles: ${roles.join(', ')}`
        )
      );
    }

    next();
  };
}

/**
 * Middleware to require admin role
 */
export const requireAdmin = requireRole('admin');

/**
 * Middleware to require admin or staff role
 */
export const requireStaff = requireRole('admin', 'staff');

/**
 * Middleware to check if user can access resource
 */
export function requireOwnershipOrRole(...roles: string[]) {
  return (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): void => {
    if (!req.user) {
      return next(new UnauthorizedError('Authentication required'));
    }

    const resourceUserId = req.params.userId || req.params.id;
    const isOwner = req.user.id === resourceUserId;
    const hasRole = roles.includes(req.user.role);

    if (!isOwner && !hasRole) {
      return next(
        new ForbiddenError(
          'Access denied. You can only access your own resources or need appropriate role'
        )
      );
    }

    next();
  };
}

/**
 * Middleware to require email verification
 */
export function requireEmailVerification(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void {
  if (!req.user) {
    return next(new UnauthorizedError('Authentication required'));
  }

  if (!req.user.email_verified) {
    return next(
      new ForbiddenError(
        'Email verification required. Please verify your email address'
      )
    );
  }

  next();
}

/**
 * Optional authentication middleware (doesn't throw if no token)
 */
export async function optionalAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.get('Authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return next();
    }

    // Check if token is blacklisted
    if (await isTokenBlacklisted(token)) {
      return next();
    }

    // Verify token
    const payload = verifyAccessToken(token);

    // Get user from tenant database
    const userResult = await queryTenant(
      payload.tenant_id,
      'SELECT id, email, first_name, last_name, phone, role, status, email_verified, last_login, created_at, updated_at FROM users WHERE id = $1',
      [payload.user_id]
    );

    if (userResult.rows.length > 0 && userResult.rows[0].status === 'active') {
      req.user = userResult.rows[0];
    }

    next();
  } catch (error) {
    // Ignore authentication errors in optional auth
    next();
  }
}

/**
 * Middleware to check tenant plan limits
 */
export function checkTenantLimits(resource: 'users' | 'products' | 'storage') {
  return async (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      if (!req.tenant) {
        return next(new UnauthorizedError('Tenant context required'));
      }

      const tenant = req.tenant;
      let currentCount = 0;
      let limit = 0;

      switch (resource) {
        case 'users':
          const userCountResult = await queryTenant(
            tenant.id,
            'SELECT COUNT(*) as count FROM users'
          );
          currentCount = parseInt(userCountResult.rows[0].count);
          limit = tenant.max_users;
          break;

        case 'products':
          const productCountResult = await queryTenant(
            tenant.id,
            'SELECT COUNT(*) as count FROM products'
          );
          currentCount = parseInt(productCountResult.rows[0].count);
          limit = tenant.max_products;
          break;

        case 'storage':
          // This would require implementing storage tracking
          // For now, just pass through
          return next();
      }

      if (currentCount >= limit) {
        return next(
          new ForbiddenError(
            `Tenant has reached the limit for ${resource}. Current: ${currentCount}, Limit: ${limit}`
          )
        );
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}