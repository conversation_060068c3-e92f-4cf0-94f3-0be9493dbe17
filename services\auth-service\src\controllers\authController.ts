import { Response } from 'express';
import {
  AuthenticatedRequest,
  ApiResponse,
  LoginRequest,
  CreateUserRequest,
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
} from '../types';
import { UserService } from '../services/userService';
import {
  verifyRefreshToken,
  generateTokenPair,
  revokeRefreshToken,
  revokeAllUserTokens,
  blacklistAccessToken,
  extractTokenFromHeader,
} from '../utils/jwt';
import { asyncHandler } from '../middleware/errorHandler';

const userService = new UserService();

/**
 * Register a new user
 */
export const register = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const userData: CreateUserRequest = req.body;
    const tenantId = req.tenant_id!;

    const user = await userService.createUser(tenantId, userData);

    const response: ApiResponse = {
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          phone: user.phone,
          role: user.role,
          status: user.status,
          email_verified: user.email_verified,
          created_at: user.created_at,
        },
      },
    };

    res.status(201).json(response);
  }
);

/**
 * Login user
 */
export const login = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { email, password }: LoginRequest = req.body;
    const tenant = req.tenant!;

    const { user, tokens } = await userService.authenticateUser(
      tenant,
      email,
      password
    );

    const response: ApiResponse = {
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          phone: user.phone,
          role: user.role,
          status: user.status,
          email_verified: user.email_verified,
          last_login: user.last_login,
        },
        tokens,
      },
    };

    res.json(response);
  }
);

/**
 * Refresh access token
 */
export const refreshToken = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { refresh_token } = req.body;

    // Verify refresh token
    const tokenData = await verifyRefreshToken(refresh_token);

    // Get user and tenant
    const user = await userService.getUserById(tokenData.tenant_id, tokenData.user_id);
    if (!user) {
      throw new Error('User not found');
    }

    const tenant = req.tenant!;

    // Generate new token pair
    const tokens = await generateTokenPair(user, tenant);

    // Revoke old refresh token
    await revokeRefreshToken(refresh_token);

    const response: ApiResponse = {
      success: true,
      message: 'Token refreshed successfully',
      data: { tokens },
    };

    res.json(response);
  }
);

/**
 * Logout user
 */
export const logout = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { refresh_token } = req.body;
    const authHeader = req.get('Authorization');
    const accessToken = extractTokenFromHeader(authHeader);

    // Revoke refresh token if provided
    if (refresh_token) {
      await revokeRefreshToken(refresh_token);
    }

    // Blacklist access token if provided
    if (accessToken) {
      await blacklistAccessToken(accessToken);
    }

    const response: ApiResponse = {
      success: true,
      message: 'Logout successful',
    };

    res.json(response);
  }
);

/**
 * Logout from all devices
 */
export const logoutAll = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const user = req.user!;
    const tenantId = req.tenant_id!;
    const authHeader = req.get('Authorization');
    const accessToken = extractTokenFromHeader(authHeader);

    // Revoke all refresh tokens for user
    await revokeAllUserTokens(user.id, tenantId);

    // Blacklist current access token
    if (accessToken) {
      await blacklistAccessToken(accessToken);
    }

    const response: ApiResponse = {
      success: true,
      message: 'Logged out from all devices successfully',
    };

    res.json(response);
  }
);

/**
 * Get current user profile
 */
export const getProfile = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const user = req.user!;

    const response: ApiResponse = {
      success: true,
      message: 'Profile retrieved successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          phone: user.phone,
          role: user.role,
          status: user.status,
          email_verified: user.email_verified,
          last_login: user.last_login,
          created_at: user.created_at,
          updated_at: user.updated_at,
        },
      },
    };

    res.json(response);
  }
);

/**
 * Update user profile
 */
export const updateProfile = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const user = req.user!;
    const tenantId = req.tenant_id!;
    const updateData = req.body;

    const updatedUser = await userService.updateUser(
      tenantId,
      user.id,
      updateData
    );

    const response: ApiResponse = {
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          first_name: updatedUser.first_name,
          last_name: updatedUser.last_name,
          phone: updatedUser.phone,
          role: updatedUser.role,
          status: updatedUser.status,
          email_verified: updatedUser.email_verified,
          last_login: updatedUser.last_login,
          created_at: updatedUser.created_at,
          updated_at: updatedUser.updated_at,
        },
      },
    };

    res.json(response);
  }
);

/**
 * Change password
 */
export const changePassword = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const user = req.user!;
    const tenantId = req.tenant_id!;
    const passwordData: ChangePasswordRequest = req.body;

    await userService.changePassword(tenantId, user.id, passwordData);

    const response: ApiResponse = {
      success: true,
      message: 'Password changed successfully',
    };

    res.json(response);
  }
);

/**
 * Forgot password
 */
export const forgotPassword = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { email }: ForgotPasswordRequest = req.body;
    const tenantId = req.tenant_id!;

    const resetToken = await userService.initiatePasswordReset(tenantId, email);

    // In a real application, you would send this token via email
    // For now, we'll return it in the response (only for development)
    const response: ApiResponse = {
      success: true,
      message: 'If the email exists, a password reset link has been sent',
      data: process.env.NODE_ENV === 'development' ? { reset_token: resetToken } : undefined,
    };

    res.json(response);
  }
);

/**
 * Reset password
 */
export const resetPassword = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { token, new_password }: ResetPasswordRequest = req.body;
    const tenantId = req.tenant_id!;

    await userService.resetPassword(tenantId, token, new_password);

    const response: ApiResponse = {
      success: true,
      message: 'Password reset successfully',
    };

    res.json(response);
  }
);

/**
 * Verify email
 */
export const verifyEmail = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { token } = req.body;
    const tenantId = req.tenant_id!;

    const user = await userService.verifyEmail(tenantId, token);

    const response: ApiResponse = {
      success: true,
      message: 'Email verified successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          email_verified: user.email_verified,
        },
      },
    };

    res.json(response);
  }
);

/**
 * Resend email verification
 */
export const resendVerification = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { email } = req.body;
    const tenantId = req.tenant_id!;

    const verificationToken = await userService.resendEmailVerification(tenantId, email);

    // In a real application, you would send this token via email
    const response: ApiResponse = {
      success: true,
      message: 'Verification email sent successfully',
      data: process.env.NODE_ENV === 'development' ? { verification_token: verificationToken } : undefined,
    };

    res.json(response);
  }
);

/**
 * Health check endpoint
 */
export const healthCheck = asyncHandler(
  async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const response: ApiResponse = {
      success: true,
      message: 'Auth service is healthy',
      data: {
        service: 'auth-service',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      },
    };

    res.json(response);
  }
);