import { Request, Response, NextFunction } from 'express';
import { AppError, ApiResponse } from '../types';

/**
 * Global error handler middleware
 */
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  let statusCode = 500;
  let message = 'Internal server error';
  let errors: string[] = [];

  // Handle operational errors
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
  }
  // Handle JWT errors
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
  }
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
  }
  // Handle database errors
  else if (error.name === 'DatabaseError' || error.name === 'QueryFailedError') {
    statusCode = 500;
    message = 'Database operation failed';

    // Log database errors for debugging
    console.error('Database Error:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });
  }
  // Handle validation errors from <PERSON><PERSON>
  else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation failed';
    errors = [error.message];
  }
  // Handle unexpected errors
  else {
    console.error('Unexpected Error:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });
  }

  // Don't expose internal error details in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Internal server error';
    errors = [];
  }

  const response: ApiResponse = {
    success: false,
    message,
    errors: errors.length > 0 ? errors : undefined,
  };

  // Add error details in development
  if (process.env.NODE_ENV === 'development') {
    response.data = {
      name: error.name,
      stack: error.stack,
    };
  }

  res.status(statusCode).json(response);
}

/**
 * 404 Not Found handler
 */
export function notFoundHandler(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const response: ApiResponse = {
    success: false,
    message: `Route ${req.method} ${req.path} not found`,
  };

  res.status(404).json(response);
}

/**
 * Async error wrapper
 */
export function asyncHandler<T extends Request, U extends Response>(
  fn: (req: T, res: U, next: NextFunction) => Promise<any>
) {
  return (req: T, res: U, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Rate limit error handler
 */
export function rateLimitHandler(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const response: ApiResponse = {
    success: false,
    message: 'Too many requests, please try again later',
  };

  res.status(429).json(response);
}