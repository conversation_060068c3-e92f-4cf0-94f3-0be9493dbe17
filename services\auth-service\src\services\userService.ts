import { v4 as uuidv4 } from 'uuid';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  ConflictError,
  NotFoundError,
  UnauthorizedError,
  ValidationError,
  TokenPair,
  Tenant,
} from '../types';
import { queryTenant, transactionTenant } from '../config/database';
import {
  hashPassword,
  comparePassword,
  validatePasswordStrength,
  generatePasswordResetToken,
  generateEmailVerificationToken,
} from '../utils/password';
import { generateTokenPair } from '../utils/jwt';

export class UserService {
  /**
   * Create a new user
   */
  async createUser(
    tenantId: string,
    userData: CreateUserRequest
  ): Promise<User> {
    // Validate password strength
    validatePasswordStrength(userData.password);

    // Check if user already exists
    const existingUser = await this.getUserByEmail(tenantId, userData.email);
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Hash password
    const passwordHash = await hashPassword(userData.password);

    // Generate email verification token
    const emailVerificationToken = generateEmailVerificationToken();

    return transactionTenant(tenantId, async (client) => {
      const result = await client.query(
        `INSERT INTO users (
          email, password_hash, first_name, last_name, phone, role,
          email_verification_token
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id, email, first_name, last_name, phone, role, status,
                  email_verified, created_at, updated_at`,
        [
          userData.email.toLowerCase(),
          passwordHash,
          userData.first_name || null,
          userData.last_name || null,
          userData.phone || null,
          userData.role || 'customer',
          emailVerificationToken,
        ]
      );

      return result.rows[0];
    });
  }

  /**
   * Authenticate user and return tokens
   */
  async authenticateUser(
    tenant: Tenant,
    email: string,
    password: string
  ): Promise<{ user: User; tokens: TokenPair }> {
    // Get user by email
    const user = await this.getUserByEmail(tenant.id, email);
    if (!user) {
      throw new UnauthorizedError('Invalid email or password');
    }

    // Check if user is active
    if (user.status !== 'active') {
      throw new UnauthorizedError('User account is not active');
    }

    // Verify password
    const isPasswordValid = await comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
      throw new UnauthorizedError('Invalid email or password');
    }

    // Generate tokens
    const tokens = await generateTokenPair(user, tenant);

    // Remove password hash from response
    const { password_hash, ...userWithoutPassword } = user;

    return {
      user: userWithoutPassword as User,
      tokens,
    };
  }

  /**
   * Get user by ID
   */
  async getUserById(tenantId: string, userId: string): Promise<User | null> {
    const result = await queryTenant(
      tenantId,
      `SELECT id, email, first_name, last_name, phone, role, status,
              email_verified, last_login, created_at, updated_at
       FROM users WHERE id = $1`,
      [userId]
    );

    return result.rows[0] || null;
  }

  /**
   * Get user by email
   */
  async getUserByEmail(tenantId: string, email: string): Promise<any> {
    const result = await queryTenant(
      tenantId,
      `SELECT id, email, password_hash, first_name, last_name, phone, role,
              status, email_verified, email_verification_token,
              password_reset_token, password_reset_expires, last_login,
              created_at, updated_at
       FROM users WHERE email = $1`,
      [email.toLowerCase()]
    );

    return result.rows[0] || null;
  }

  /**
   * Update user
   */
  async updateUser(
    tenantId: string,
    userId: string,
    updateData: UpdateUserRequest
  ): Promise<User> {
    const user = await this.getUserById(tenantId, userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    if (updateData.first_name !== undefined) {
      updateFields.push(`first_name = $${paramIndex++}`);
      updateValues.push(updateData.first_name);
    }

    if (updateData.last_name !== undefined) {
      updateFields.push(`last_name = $${paramIndex++}`);
      updateValues.push(updateData.last_name);
    }

    if (updateData.phone !== undefined) {
      updateFields.push(`phone = $${paramIndex++}`);
      updateValues.push(updateData.phone);
    }

    if (updateData.status !== undefined) {
      updateFields.push(`status = $${paramIndex++}`);
      updateValues.push(updateData.status);
    }

    if (updateFields.length === 0) {
      return user;
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    updateValues.push(userId);

    const result = await queryTenant(
      tenantId,
      `UPDATE users SET ${updateFields.join(', ')}
       WHERE id = $${paramIndex}
       RETURNING id, email, first_name, last_name, phone, role, status,
                 email_verified, last_login, created_at, updated_at`,
      updateValues
    );

    return result.rows[0];
  }

  /**
   * Change user password
   */
  async changePassword(
    tenantId: string,
    userId: string,
    passwordData: ChangePasswordRequest
  ): Promise<void> {
    const user = await queryTenant(
      tenantId,
      'SELECT password_hash FROM users WHERE id = $1',
      [userId]
    );

    if (user.rows.length === 0) {
      throw new NotFoundError('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await comparePassword(
      passwordData.current_password,
      user.rows[0].password_hash
    );

    if (!isCurrentPasswordValid) {
      throw new UnauthorizedError('Current password is incorrect');
    }

    // Validate new password strength
    validatePasswordStrength(passwordData.new_password);

    // Hash new password
    const newPasswordHash = await hashPassword(passwordData.new_password);

    // Update password
    await queryTenant(
      tenantId,
      'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [newPasswordHash, userId]
    );
  }

  /**
   * Initiate password reset
   */
  async initiatePasswordReset(
    tenantId: string,
    email: string
  ): Promise<string> {
    const user = await this.getUserByEmail(tenantId, email);
    if (!user) {
      // Don't reveal if user exists or not
      return 'If the email exists, a password reset link has been sent';
    }

    // Generate password reset token
    const resetToken = generatePasswordResetToken();
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    await queryTenant(
      tenantId,
      `UPDATE users SET
        password_reset_token = $1,
        password_reset_expires = $2,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = $3`,
      [resetToken, resetExpires, user.id]
    );

    return resetToken;
  }

  /**
   * Reset password with token
   */
  async resetPassword(
    tenantId: string,
    token: string,
    newPassword: string
  ): Promise<void> {
    const user = await queryTenant(
      tenantId,
      `SELECT id, password_reset_token, password_reset_expires
       FROM users
       WHERE password_reset_token = $1 AND password_reset_expires > CURRENT_TIMESTAMP`,
      [token]
    );

    if (user.rows.length === 0) {
      throw new UnauthorizedError('Invalid or expired reset token');
    }

    // Validate new password strength
    validatePasswordStrength(newPassword);

    // Hash new password
    const passwordHash = await hashPassword(newPassword);

    // Update password and clear reset token
    await queryTenant(
      tenantId,
      `UPDATE users SET
        password_hash = $1,
        password_reset_token = NULL,
        password_reset_expires = NULL,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = $2`,
      [passwordHash, user.rows[0].id]
    );
  }

  /**
   * Verify email with token
   */
  async verifyEmail(tenantId: string, token: string): Promise<User> {
    const user = await queryTenant(
      tenantId,
      `SELECT id FROM users WHERE email_verification_token = $1`,
      [token]
    );

    if (user.rows.length === 0) {
      throw new UnauthorizedError('Invalid verification token');
    }

    const result = await queryTenant(
      tenantId,
      `UPDATE users SET
        email_verified = true,
        email_verification_token = NULL,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = $1
       RETURNING id, email, first_name, last_name, phone, role, status,
                 email_verified, last_login, created_at, updated_at`,
      [user.rows[0].id]
    );

    return result.rows[0];
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(
    tenantId: string,
    email: string
  ): Promise<string> {
    const user = await this.getUserByEmail(tenantId, email);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    if (user.email_verified) {
      throw new ValidationError('Email is already verified');
    }

    // Generate new verification token
    const verificationToken = generateEmailVerificationToken();

    await queryTenant(
      tenantId,
      `UPDATE users SET
        email_verification_token = $1,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = $2`,
      [verificationToken, user.id]
    );

    return verificationToken;
  }

  /**
   * Delete user
   */
  async deleteUser(tenantId: string, userId: string): Promise<void> {
    const user = await this.getUserById(tenantId, userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    await queryTenant(tenantId, 'DELETE FROM users WHERE id = $1', [userId]);
  }
}