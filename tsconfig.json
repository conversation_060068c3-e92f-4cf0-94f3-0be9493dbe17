{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "baseUrl": "./", "paths": {"@shared/*": ["shared/*"], "@services/*": ["services/*"], "@tests/*": ["tests/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["services/**/*", "shared/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}