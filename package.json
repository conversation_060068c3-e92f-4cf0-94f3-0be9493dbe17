{"name": "multi-tenant-ecommerce", "version": "1.0.0", "description": "Multi-tenant e-commerce system with Node.js and FastAPI microservices", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:gateway\" \"npm run dev:auth\" \"npm run dev:file\" \"npm run dev:notification\"", "dev:gateway": "cd services/api-gateway && npm run dev", "dev:auth": "cd services/auth-service && npm run dev", "dev:file": "cd services/file-service && npm run dev", "dev:notification": "cd services/notification-service && npm run dev", "build": "npm run build:gateway && npm run build:auth && npm run build:file && npm run build:notification", "build:gateway": "cd services/api-gateway && npm run build", "build:auth": "cd services/auth-service && npm run build", "build:file": "cd services/file-service && npm run build", "build:notification": "cd services/notification-service && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "format": "prettier --write \"**/*.{ts,js,json,md}\"", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker-compose.prod.yml up", "setup:services": "npm run setup:gateway && npm run setup:auth && npm run setup:file && npm run setup:notification", "setup:gateway": "cd services/api-gateway && npm install", "setup:auth": "cd services/auth-service && npm install", "setup:file": "cd services/file-service && npm install", "setup:notification": "cd services/notification-service && npm install", "db:migrate": "cd shared/database && node migrate.js", "db:create-tenant": "cd shared/database && node migrate.js create-tenant", "db:seed-tenant": "cd shared/database && node migrate.js seed-tenant", "db:list-tenants": "cd shared/database && node migrate.js list-tenants", "db:drop-tenant": "cd shared/database && node migrate.js drop-tenant"}, "keywords": ["multi-tenant", "ecommerce", "microservices", "nodejs", "<PERSON><PERSON><PERSON>", "typescript"], "author": "TruongTech", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.6.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "concurrently": "^8.2.1", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.0.3", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "workspaces": ["services/*", "shared/*"]}