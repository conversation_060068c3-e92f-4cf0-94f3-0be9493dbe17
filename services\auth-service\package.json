{"name": "@ecommerce/auth-service", "version": "1.0.0", "description": "Authentication and authorization service for multi-tenant e-commerce", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"express": "^4.18.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.10.1", "pg": "^8.11.3", "redis": "^4.6.8", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "dotenv": "^16.3.1", "uuid": "^9.0.0", "nodemailer": "^6.9.4"}, "devDependencies": {"@types/express": "^4.17.17", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/cors": "^2.8.13", "@types/uuid": "^9.0.2", "@types/nodemailer": "^6.4.9", "nodemon": "^3.0.1", "ts-node": "^10.9.1"}, "keywords": ["authentication", "authorization", "jwt", "multi-tenant", "microservice"], "author": "TruongTech", "license": "MIT"}