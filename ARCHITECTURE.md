# Multi-Tenant E-commerce System - Hybrid Architecture

## Overview
Hệ thống thương mại điện tử multi-tenants sử dụng kiến trúc microservices hybrid với Node.js và FastAPI để tối ưu hiệu suất và khả năng mở rộng.

## Kiến trúc Microservices

### Service Distribution Strategy

#### Node.js Services
- **API Gateway Service** (Express.js + TypeScript)
  - Request routing và load balancing
  - Authentication middleware
  - Rate limiting và security
  - CORS handling

- **Auth Service** (Express.js + TypeScript)
  - JWT token management
  - User authentication
  - Session management
  - Password reset functionality

- **File Service** (Express.js + TypeScript)
  - Image/file upload handling
  - Media processing
  - CDN integration
  - File storage management

- **Notification Service** (Express.js + TypeScript)
  - Email notifications
  - Real-time notifications (WebSocket)
  - Push notifications
  - SMS integration

#### FastAPI Services
- **Tenant Service** (FastAPI + Python)
  - Tenant management CRUD
  - Tenant registration
  - Database provisioning
  - Tenant configuration

- **Product Service** (FastAPI + Python)
  - Product catalog management
  - Category management
  - Inventory tracking
  - Search functionality

- **Order Service** (FastAPI + Python)
  - Order processing
  - Cart management
  - Payment integration
  - Order fulfillment

- **Analytics Service** (FastAPI + Python)
  - Sales analytics
  - Customer insights
  - Performance metrics
  - Reporting dashboard

- **Recommendation Service** (FastAPI + Python)
  - Product recommendations
  - ML-based suggestions
  - Customer behavior analysis
  - Personalization engine

## Technology Stack

### Node.js Stack
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Validation**: Joi
- **File Upload**: Multer
- **WebSocket**: Socket.io
- **Testing**: Jest + Supertest

### FastAPI Stack
- **Runtime**: Python 3.11+
- **Framework**: FastAPI
- **ORM**: SQLAlchemy + Alembic
- **Validation**: Pydantic
- **Testing**: Pytest + httpx
- **Background Tasks**: Celery + Redis

### Shared Infrastructure
- **Database**: PostgreSQL 15+
- **Cache**: Redis 7+
- **Message Queue**: RabbitMQ/Redis
- **API Gateway**: Nginx/Kong
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack

## Database Architecture

### Master Database (System Level)
```sql
-- Tenants
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    custom_domain VARCHAR(255),
    database_name VARCHAR(100) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    plan VARCHAR(20) DEFAULT 'basic' CHECK (plan IN ('basic', 'premium', 'enterprise')),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- System Admins
CREATE TABLE system_admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'support' CHECK (role IN ('super_admin', 'support')),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Service Registry
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) CHECK (type IN ('nodejs', 'fastapi')),
    url VARCHAR(255) NOT NULL,
    health_check_url VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    version VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tenant Database Schema
```sql
-- Users (Customers + Tenant Admins)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'staff', 'customer')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    email_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Categories
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id),
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    seo_title VARCHAR(255),
    seo_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Products
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description TEXT,
    sku VARCHAR(100) UNIQUE NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    compare_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    track_inventory BOOLEAN DEFAULT true,
    inventory_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 5,
    weight DECIMAL(8,2),
    dimensions JSONB, -- {length, width, height}
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('active', 'draft', 'archived')),
    featured BOOLEAN DEFAULT false,
    seo_title VARCHAR(255),
    seo_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product Images
CREATE TABLE product_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    url VARCHAR(500) NOT NULL,
    alt_text VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product Categories (Many-to-Many)
CREATE TABLE product_categories (
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    PRIMARY KEY (product_id, category_id)
);

-- Customer Addresses
CREATE TABLE addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(20) DEFAULT 'shipping' CHECK (type IN ('billing', 'shipping')),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    company VARCHAR(100),
    address_line_1 VARCHAR(255) NOT NULL,
    address_line_2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(2) NOT NULL, -- ISO country code
    phone VARCHAR(20),
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Shopping Cart
CREATE TABLE cart_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, product_id)
);

-- Orders
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    billing_address JSONB NOT NULL,
    shipping_address JSONB NOT NULL,
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded', 'partially_refunded')),
    payment_method VARCHAR(50),
    payment_reference VARCHAR(255),
    notes TEXT,
    shipped_at TIMESTAMP,
    delivered_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Order Items
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    product_name VARCHAR(255) NOT NULL,
    product_sku VARCHAR(100) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tenant Settings
CREATE TABLE settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    type VARCHAR(20) DEFAULT 'string' CHECK (type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Architecture

### Service Communication
- **Synchronous**: HTTP/REST APIs giữa các services
- **Asynchronous**: Message queues (RabbitMQ/Redis) cho background tasks
- **Real-time**: WebSocket cho notifications

### API Gateway Pattern
```
Client Request → API Gateway → Service Router → Target Service
                     ↓
              Authentication & Authorization
                     ↓
              Rate Limiting & Logging
```

### Service Endpoints

#### Node.js Services
```
API Gateway (Port 3000)
├── /api/v1/auth/* → Auth Service
├── /api/v1/files/* → File Service
├── /api/v1/notifications/* → Notification Service
└── /api/v1/* → Route to FastAPI services

Auth Service (Port 3001)
├── POST /login
├── POST /register
├── POST /logout
├── POST /refresh-token
├── POST /forgot-password
└── POST /reset-password

File Service (Port 3002)
├── POST /upload
├── GET /files/:id
├── DELETE /files/:id
└── POST /process-image

Notification Service (Port 3003)
├── POST /email
├── POST /sms
├── WebSocket /notifications
└── POST /push
```

#### FastAPI Services
```
Tenant Service (Port 8001)
├── GET /tenants
├── POST /tenants
├── GET /tenants/:id
├── PUT /tenants/:id
├── DELETE /tenants/:id
└── POST /tenants/:id/provision-db

Product Service (Port 8002)
├── GET /products
├── POST /products
├── GET /products/:id
├── PUT /products/:id
├── DELETE /products/:id
├── GET /categories
├── POST /categories
└── GET /search

Order Service (Port 8003)
├── GET /orders
├── POST /orders
├── GET /orders/:id
├── PUT /orders/:id/status
├── GET /cart
├── POST /cart/items
└── DELETE /cart/items/:id

Analytics Service (Port 8004)
├── GET /dashboard
├── GET /sales-report
├── GET /customer-insights
└── GET /performance-metrics

Recommendation Service (Port 8005)
├── GET /recommendations/:user_id
├── GET /trending-products
├── GET /related-products/:product_id
└── POST /track-behavior
```

## Security & Multi-tenancy

### Tenant Isolation
- **Database Level**: Mỗi tenant có database riêng
- **Application Level**: Tenant context trong mọi request
- **API Level**: Tenant validation middleware

### Authentication Flow
```
1. Client → API Gateway (với subdomain/domain)
2. API Gateway → Tenant identification
3. API Gateway → Auth Service (validate JWT)
4. Auth Service → Return user + tenant context
5. API Gateway → Route to target service với tenant context
```

### Security Measures
- JWT tokens với tenant_id và user_id
- Rate limiting per tenant
- Input validation và sanitization
- SQL injection prevention
- XSS protection
- CORS configuration
- File upload security

## Deployment Architecture

### Development Environment
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  # Databases
  postgres-master:
    image: postgres:15
    environment:
      POSTGRES_DB: ecommerce_master
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  # Node.js Services
  api-gateway:
    build: ./services/api-gateway
    ports:
      - "3000:3000"
    depends_on:
      - redis

  auth-service:
    build: ./services/auth-service
    ports:
      - "3001:3001"
    depends_on:
      - postgres-master
      - redis

  # FastAPI Services
  tenant-service:
    build: ./services/tenant-service
    ports:
      - "8001:8001"
    depends_on:
      - postgres-master

  product-service:
    build: ./services/product-service
    ports:
      - "8002:8002"
```

### Production Environment
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl

  # Load balancer cho services
  api-gateway:
    image: ecommerce/api-gateway:latest
    deploy:
      replicas: 3
    environment:
      NODE_ENV: production

  # Database cluster
  postgres-master:
    image: postgres:15
    environment:
      POSTGRES_DB: ecommerce_master
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Monitoring
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
```

## Project Structure
```
multi-tenant-ecommerce/
├── services/
│   ├── api-gateway/          # Node.js API Gateway
│   ├── auth-service/         # Node.js Auth Service
│   ├── file-service/         # Node.js File Service
│   ├── notification-service/ # Node.js Notification Service
│   ├── tenant-service/       # FastAPI Tenant Service
│   ├── product-service/      # FastAPI Product Service
│   ├── order-service/        # FastAPI Order Service
│   ├── analytics-service/    # FastAPI Analytics Service
│   └── recommendation-service/ # FastAPI Recommendation Service
├── shared/
│   ├── database/
│   │   ├── migrations/
│   │   └── seeds/
│   ├── types/               # Shared TypeScript types
│   ├── utils/               # Shared utilities
│   └── config/              # Shared configuration
├── infrastructure/
│   ├── docker/
│   ├── kubernetes/
│   ├── terraform/
│   └── monitoring/
├── docs/
│   ├── api/
│   ├── deployment/
│   └── development/
├── tests/
│   ├── integration/
│   ├── e2e/
│   └── load/
├── docker-compose.dev.yml
├── docker-compose.prod.yml
├── ARCHITECTURE.md
└── README.md
```

## Development Workflow

### Phase 1: Foundation (Weeks 1-2)
1. Setup project structure
2. Configure development environment
3. Implement API Gateway
4. Setup master database

### Phase 2: Core Services (Weeks 3-4)
1. Auth Service (Node.js)
2. Tenant Service (FastAPI)
3. Basic tenant management

### Phase 3: E-commerce Core (Weeks 5-6)
1. Product Service (FastAPI)
2. Order Service (FastAPI)
3. File Service (Node.js)

### Phase 4: Advanced Features (Weeks 7-8)
1. Analytics Service (FastAPI)
2. Recommendation Service (FastAPI)
3. Notification Service (Node.js)

### Phase 5: Testing & Deployment (Weeks 9-10)
1. Comprehensive testing
2. Performance optimization
3. Production deployment
4. Documentation

## Next Steps
1. ✅ Architecture design completed
2. 🔄 Setup project structure
3. ⏳ Implement API Gateway
4. ⏳ Develop Auth Service
5. ⏳ Build Tenant Service