import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { JwtPayload, TokenPair, User, Tenant } from '../types';
import { redisClient } from '../config/database';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d';

/**
 * Generate access token
 */
export function generateAccessToken(user: User, tenant: Tenant): string {
  const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
    user_id: user.id,
    tenant_id: tenant.id,
    email: user.email,
    role: user.role,
  };

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'ecommerce-auth-service',
    audience: tenant.subdomain,
  });
}

/**
 * Generate refresh token
 */
export function generateRefreshToken(): string {
  return uuidv4();
}

/**
 * Generate token pair (access + refresh)
 */
export async function generateTokenPair(
  user: User,
  tenant: Tenant
): Promise<TokenPair> {
  const accessToken = generateAccessToken(user, tenant);
  const refreshToken = generateRefreshToken();

  // Store refresh token in Redis with expiration
  const refreshTokenKey = `refresh_token:${refreshToken}`;
  const refreshTokenData = {
    user_id: user.id,
    tenant_id: tenant.id,
    created_at: new Date().toISOString(),
  };

  await redisClient.setEx(
    refreshTokenKey,
    7 * 24 * 60 * 60, // 7 days in seconds
    JSON.stringify(refreshTokenData)
  );

  // Get token expiration time
  const decoded = jwt.decode(accessToken) as JwtPayload;
  const expiresIn = decoded.exp - decoded.iat;

  return {
    access_token: accessToken,
    refresh_token: refreshToken,
    expires_in: expiresIn,
  };
}

/**
 * Verify access token
 */
export function verifyAccessToken(token: string): JwtPayload {
  try {
    return jwt.verify(token, JWT_SECRET) as JwtPayload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid token');
    } else {
      throw new Error('Token verification failed');
    }
  }
}

/**
 * Verify refresh token
 */
export async function verifyRefreshToken(
  refreshToken: string
): Promise<{ user_id: string; tenant_id: string }> {
  const refreshTokenKey = `refresh_token:${refreshToken}`;
  const tokenData = await redisClient.get(refreshTokenKey);

  if (!tokenData) {
    throw new Error('Invalid or expired refresh token');
  }

  return JSON.parse(tokenData);
}

/**
 * Revoke refresh token
 */
export async function revokeRefreshToken(refreshToken: string): Promise<void> {
  const refreshTokenKey = `refresh_token:${refreshToken}`;
  await redisClient.del(refreshTokenKey);
}

/**
 * Revoke all refresh tokens for a user
 */
export async function revokeAllUserTokens(
  userId: string,
  tenantId: string
): Promise<void> {
  // Get all refresh token keys
  const pattern = 'refresh_token:*';
  const keys = await redisClient.keys(pattern);

  // Check each token and delete if it belongs to the user
  for (const key of keys) {
    const tokenData = await redisClient.get(key);
    if (tokenData) {
      const parsed = JSON.parse(tokenData);
      if (parsed.user_id === userId && parsed.tenant_id === tenantId) {
        await redisClient.del(key);
      }
    }
  }
}

/**
 * Blacklist access token (for logout)
 */
export async function blacklistAccessToken(token: string): Promise<void> {
  try {
    const decoded = verifyAccessToken(token);
    const blacklistKey = `blacklist:${token}`;
    const ttl = decoded.exp - Math.floor(Date.now() / 1000);

    if (ttl > 0) {
      await redisClient.setEx(blacklistKey, ttl, 'blacklisted');
    }
  } catch (error) {
    // Token is already invalid, no need to blacklist
  }
}

/**
 * Check if access token is blacklisted
 */
export async function isTokenBlacklisted(token: string): Promise<boolean> {
  const blacklistKey = `blacklist:${token}`;
  const result = await redisClient.get(blacklistKey);
  return result === 'blacklisted';
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(authHeader?: string): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

/**
 * Get token expiration time
 */
export function getTokenExpiration(token: string): number {
  try {
    const decoded = jwt.decode(token) as JwtPayload;
    return decoded.exp;
  } catch (error) {
    throw new Error('Invalid token');
  }
}

/**
 * Check if token is expired
 */
export function isTokenExpired(token: string): boolean {
  try {
    const exp = getTokenExpiration(token);
    return Date.now() >= exp * 1000;
  } catch (error) {
    return true;
  }
}