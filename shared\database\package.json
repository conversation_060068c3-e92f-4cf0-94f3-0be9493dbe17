{"name": "@ecommerce/database", "version": "1.0.0", "description": "Database utilities and migrations for multi-tenant e-commerce system", "main": "migrate.js", "scripts": {"migrate": "node migrate.js", "create-tenant": "node migrate.js create-tenant", "seed-tenant": "node migrate.js seed-tenant", "list-tenants": "node migrate.js list-tenants", "drop-tenant": "node migrate.js drop-tenant"}, "dependencies": {"pg": "^8.11.3"}, "keywords": ["database", "migration", "multi-tenant", "postgresql"], "author": "TruongTech", "license": "MIT"}