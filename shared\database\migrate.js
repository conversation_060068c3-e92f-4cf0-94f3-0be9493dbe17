#!/usr/bin/env node

/**
 * Database Migration Script for Multi-tenant E-commerce System
 *
 * Usage:
 * node migrate.js create-tenant <tenant_name> <subdomain>
 * node migrate.js seed-tenant <tenant_id>
 * node migrate.js list-tenants
 * node migrate.js drop-tenant <tenant_id>
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const masterDbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  user: process.env.DB_USER || 'admin',
  password: process.env.DB_PASSWORD || 'password123',
  database: process.env.DB_NAME || 'ecommerce_master',
};

class DatabaseMigrator {
  constructor() {
    this.masterPool = new Pool(masterDbConfig);
  }

  async createTenantDatabase(tenantName, subdomain) {
    const client = await this.masterPool.connect();

    try {
      await client.query('BEGIN');

      // Generate unique database name
      const dbName = `tenant_${subdomain.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;

      // Check if tenant already exists
      const existingTenant = await client.query(
        'SELECT id FROM tenants WHERE subdomain = $1 OR database_name = $2',
        [subdomain, dbName]
      );

      if (existingTenant.rows.length > 0) {
        throw new Error(`Tenant with subdomain '${subdomain}' or database '${dbName}' already exists`);
      }

      // Create tenant record
      const tenantResult = await client.query(
        `INSERT INTO tenants (name, subdomain, database_name, status, plan)
         VALUES ($1, $2, $3, 'active', 'basic')
         RETURNING id, database_name`,
        [tenantName, subdomain, dbName]
      );

      const tenant = tenantResult.rows[0];

      // Create tenant database
      await client.query(`CREATE DATABASE "${dbName}"`);

      await client.query('COMMIT');

      console.log(`✅ Tenant created successfully:`);
      console.log(`   ID: ${tenant.id}`);
      console.log(`   Name: ${tenantName}`);
      console.log(`   Subdomain: ${subdomain}`);
      console.log(`   Database: ${dbName}`);

      // Initialize tenant database schema
      await this.initializeTenantSchema(dbName);

      return tenant;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  async initializeTenantSchema(dbName) {
    const tenantDbConfig = {
      ...masterDbConfig,
      database: dbName,
    };

    const tenantPool = new Pool(tenantDbConfig);
    const client = await tenantPool.connect();

    try {
      console.log(`🔧 Initializing schema for database: ${dbName}`);

      // Read and execute tenant schema template
      const schemaPath = path.join(__dirname, 'migrations', 'tenant-schema-template.sql');
      const schemaSql = fs.readFileSync(schemaPath, 'utf8');

      await client.query(schemaSql);

      console.log(`✅ Schema initialized for database: ${dbName}`);

    } catch (error) {
      console.error(`❌ Error initializing schema for ${dbName}:`, error.message);
      throw error;
    } finally {
      client.release();
      await tenantPool.end();
    }
  }

  async seedTenantDatabase(tenantId) {
    const client = await this.masterPool.connect();

    try {
      // Get tenant database name
      const tenantResult = await client.query(
        'SELECT database_name, name FROM tenants WHERE id = $1',
        [tenantId]
      );

      if (tenantResult.rows.length === 0) {
        throw new Error(`Tenant with ID '${tenantId}' not found`);
      }

      const { database_name: dbName, name: tenantName } = tenantResult.rows[0];

      console.log(`🌱 Seeding database: ${dbName} for tenant: ${tenantName}`);

      // Connect to tenant database
      const tenantDbConfig = {
        ...masterDbConfig,
        database: dbName,
      };

      const tenantPool = new Pool(tenantDbConfig);
      const tenantClient = await tenantPool.connect();

      try {
        // Read and execute seed data
        const seedPath = path.join(__dirname, 'seeds', 'tenant-seed-data.sql');
        const seedSql = fs.readFileSync(seedPath, 'utf8');

        await tenantClient.query(seedSql);

        console.log(`✅ Database seeded successfully for tenant: ${tenantName}`);

      } finally {
        tenantClient.release();
        await tenantPool.end();
      }

    } catch (error) {
      console.error(`❌ Error seeding tenant database:`, error.message);
      throw error;
    } finally {
      client.release();
    }
  }

  async listTenants() {
    const client = await this.masterPool.connect();

    try {
      const result = await client.query(`
        SELECT id, name, subdomain, database_name, status, plan, created_at
        FROM tenants
        ORDER BY created_at DESC
      `);

      if (result.rows.length === 0) {
        console.log('📋 No tenants found');
        return;
      }

      console.log('📋 Tenants List:');
      console.log('─'.repeat(100));
      console.log('ID'.padEnd(36) + ' | ' + 'Name'.padEnd(20) + ' | ' + 'Subdomain'.padEnd(15) + ' | ' + 'Status'.padEnd(8) + ' | ' + 'Plan'.padEnd(10) + ' | ' + 'Created');
      console.log('─'.repeat(100));

      result.rows.forEach(tenant => {
        const createdAt = new Date(tenant.created_at).toLocaleDateString();
        console.log(
          tenant.id.padEnd(36) + ' | ' +
          tenant.name.padEnd(20) + ' | ' +
          tenant.subdomain.padEnd(15) + ' | ' +
          tenant.status.padEnd(8) + ' | ' +
          tenant.plan.padEnd(10) + ' | ' +
          createdAt
        );
      });

    } finally {
      client.release();
    }
  }

  async dropTenant(tenantId) {
    const client = await this.masterPool.connect();

    try {
      await client.query('BEGIN');

      // Get tenant info
      const tenantResult = await client.query(
        'SELECT database_name, name FROM tenants WHERE id = $1',
        [tenantId]
      );

      if (tenantResult.rows.length === 0) {
        throw new Error(`Tenant with ID '${tenantId}' not found`);
      }

      const { database_name: dbName, name: tenantName } = tenantResult.rows[0];

      console.log(`🗑️  Dropping tenant: ${tenantName} (Database: ${dbName})`);

      // Drop tenant database
      await client.query(`DROP DATABASE IF EXISTS "${dbName}"`);

      // Delete tenant record
      await client.query('DELETE FROM tenants WHERE id = $1', [tenantId]);

      await client.query('COMMIT');

      console.log(`✅ Tenant '${tenantName}' dropped successfully`);

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  async close() {
    await this.masterPool.end();
  }
}

// Main function to handle CLI commands
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command) {
    console.log(`
🏪 Multi-tenant E-commerce Database Migrator

Usage:
  node migrate.js create-tenant <tenant_name> <subdomain>
  node migrate.js seed-tenant <tenant_id>
  node migrate.js list-tenants
  node migrate.js drop-tenant <tenant_id>

Examples:
  node migrate.js create-tenant "My Store" mystore
  node migrate.js seed-tenant 123e4567-e89b-12d3-a456-426614174000
  node migrate.js list-tenants
  node migrate.js drop-tenant 123e4567-e89b-12d3-a456-426614174000
    `);
    process.exit(1);
  }

  const migrator = new DatabaseMigrator();

  try {
    switch (command) {
      case 'create-tenant': {
        const tenantName = args[1];
        const subdomain = args[2];

        if (!tenantName || !subdomain) {
          console.error('❌ Error: tenant_name and subdomain are required');
          console.log('Usage: node migrate.js create-tenant <tenant_name> <subdomain>');
          process.exit(1);
        }

        // Validate subdomain format
        if (!/^[a-z0-9-]+$/.test(subdomain)) {
          console.error('❌ Error: subdomain must contain only lowercase letters, numbers, and hyphens');
          process.exit(1);
        }

        const tenant = await migrator.createTenantDatabase(tenantName, subdomain);

        // Automatically seed the new tenant
        console.log('🌱 Seeding new tenant database...');
        await migrator.seedTenantDatabase(tenant.id);

        console.log(`\n🎉 Tenant setup completed! You can now access it at: ${subdomain}.yourdomain.com`);
        break;
      }

      case 'seed-tenant': {
        const tenantId = args[1];

        if (!tenantId) {
          console.error('❌ Error: tenant_id is required');
          console.log('Usage: node migrate.js seed-tenant <tenant_id>');
          process.exit(1);
        }

        await migrator.seedTenantDatabase(tenantId);
        break;
      }

      case 'list-tenants': {
        await migrator.listTenants();
        break;
      }

      case 'drop-tenant': {
        const tenantId = args[1];

        if (!tenantId) {
          console.error('❌ Error: tenant_id is required');
          console.log('Usage: node migrate.js drop-tenant <tenant_id>');
          process.exit(1);
        }

        // Confirmation prompt
        console.log('⚠️  WARNING: This will permanently delete the tenant and all its data!');
        console.log('Type "yes" to confirm:');

        const readline = require('readline');
        const rl = readline.createInterface({
          input: process.stdin,
          output: process.stdout
        });

        const confirmation = await new Promise(resolve => {
          rl.question('', resolve);
        });

        rl.close();

        if (confirmation.toLowerCase() !== 'yes') {
          console.log('❌ Operation cancelled');
          process.exit(0);
        }

        await migrator.dropTenant(tenantId);
        break;
      }

      default:
        console.error(`❌ Error: Unknown command '${command}'`);
        console.log('Run "node migrate.js" without arguments to see usage');
        process.exit(1);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    await migrator.close();
  }
}

// Run the main function if this script is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { DatabaseMigrator };